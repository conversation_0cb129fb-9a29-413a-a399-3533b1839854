在功能区域也需要有积分显示，要有感知进度条，详细参考background-remover-free的功能区域，

对接background remover free的api：
Import and set up the client:
import Replicate from "replicate";
import fs from "node:fs";

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});


Run 851-labs/background-remover using Replicate’s API. Check out the model's schema for an overview of inputs and outputs.

const output = await replicate.run(
  "851-labs/background-remover:a029dff38972b5fda4ec5d75d7d1cd25aeff621d2cf4946a41055d7db66b80bc",
  {
    input: {
      image: "https://example.com/1.webp",
      format: "png",
      reverse: false,
      threshold: 0,
      background_type: "rgba"
    }
  }
);

// To access the file URL:
console.log(output.url()); //=> "http://example.com"

// To write the file to disk:
fs.writeFile("my-image.png", output);



---
Input schema json格式：

{
  "type": "object",
  "title": "Input",
  "required": [
    "image"
  ],
  "properties": {
    "image": {
      "type": "string",
      "title": "Image",
      "format": "uri",
      "x-order": 0,
      "description": "Input image"
    },
    "format": {
      "type": "string",
      "title": "Format",
      "default": "png",
      "x-order": 4,
      "description": "Output format (e.g., png, jpg). Defaults to png."
    },
    "reverse": {
      "type": "boolean",
      "title": "Reverse",
      "default": false,
      "x-order": 2,
      "description": "If True, remove the foreground instead of the background."
    },
    "threshold": {
      "type": "number",
      "title": "Threshold",
      "default": 0,
      "x-order": 1,
      "description": "Threshold for hard segmentation (0.0-1.0). If 0.0, uses soft alpha."
    },
    "background_type": {
      "type": "string",
      "title": "Background Type",
      "default": "rgba",
      "x-order": 3,
      "description": "Background type: 'rgba', 'map', 'green', 'white', [R,G,B] array, 'blur', 'overlay', or path to an image."
    }
  }
}



Output schema格式：
{
  "type": "string",
  "title": "Output",
  "format": "uri"
}