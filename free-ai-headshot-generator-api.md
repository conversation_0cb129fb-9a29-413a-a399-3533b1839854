Import and set up the client:

import Replicate from "replicate";
import fs from "node:fs";

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

Run flux-kontext-apps/professional-headshot using Replicate’s API. Check out the model's schema for an overview of inputs and outputs.

const input = {
  gender: "female",
  background: "neutral",
  input_image: "https://replicate.delivery/pbxt/N59WPITgSJ2OAsuLmW3FfKJJ47Y61BeCZJ7FXenJp4WdcfNN/tmpozgjnvve.png",
  aspect_ratio: "1:1",
  output_format: "png",
  safety_tolerance: 2
};

const output = await replicate.run("flux-kontext-apps/professional-headshot", { input });

// To access the file URL:
console.log(output.url()); //=> "http://example.com"

// To write the file to disk:
fs.writeFile("my-image.png", output);


这是输入的参数，Input schema格式：
{
  "type": "object",
  "title": "Input",
  "required": [
    "input_image"
  ],
  "properties": {
    "seed": {
      "type": "integer",
      "title": "Seed",
      "x-order": 4,
      "nullable": true,
      "description": "Random seed. Set for reproducible generation"
    },
    "gender": {
      "enum": [
        "none",
        "male",
        "female"
      ],
      "type": "string",
      "title": "gender",
      "description": "The gender of the person",
      "default": "none",
      "x-order": 1
    },
    "background": {
      "enum": [
        "white",
        "black",
        "neutral",
        "gray",
        "office"
      ],
      "type": "string",
      "title": "background",
      "description": "The background of the headshot",
      "default": "neutral",
      "x-order": 2
    },
    "input_image": {
      "type": "string",
      "title": "Input Image",
      "format": "uri",
      "x-order": 0,
      "description": "Image of the person to create a professional headshot for. Must be jpeg, png, gif, or webp."
    },
    "aspect_ratio": {
      "enum": [
        "match_input_image",
        "1:1",
        "16:9",
        "9:16",
        "4:3",
        "3:4",
        "3:2",
        "2:3",
        "4:5",
        "5:4",
        "21:9",
        "9:21",
        "2:1",
        "1:2"
      ],
      "type": "string",
      "title": "aspect_ratio",
      "description": "Aspect ratio of the generated image. Use 'match_input_image' to match the aspect ratio of the input image.",
      "default": "match_input_image",
      "x-order": 3
    },
    "output_format": {
      "enum": [
        "jpg",
        "png"
      ],
      "type": "string",
      "title": "output_format",
      "description": "Output format for the generated image",
      "default": "png",
      "x-order": 5
    },
    "safety_tolerance": {
      "type": "integer",
      "title": "Safety Tolerance",
      "default": 2,
      "maximum": 2,
      "minimum": 0,
      "x-order": 6,
      "description": "Safety tolerance, 0 is most strict and 2 is most permissive. 2 is currently the maximum allowed."
    }
  }
}

这是输出的格式：Output schema
{
  "type": "string",
  "title": "Output",
  "format": "uri"
}


