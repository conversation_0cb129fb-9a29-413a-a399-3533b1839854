"use client";

import { useState, useRef, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

import { Upload, Download, Loader2, X, ExternalLink } from 'lucide-react';
import { toast } from 'sonner';
import Image from 'next/image';
import { v4 as uuidv4 } from 'uuid';

interface BackgroundRemoverFreeProps {
  title?: string;
}

export default function BackgroundRemoverFree({
  title = "Background Remover Free"
}: BackgroundRemoverFreeProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // State management
  const [mounted, setMounted] = useState(false);
  const [userCredits, setUserCredits] = useState<number | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [perceptionProgress, setPerceptionProgress] = useState(0);
  const [processedImageUrl, setProcessedImageUrl] = useState<string>('');
  const [isUploading, setIsUploading] = useState(false);
  const [actualProcessingStarted, setActualProcessingStarted] = useState(false);



  // Component mounting
  useEffect(() => {
    setMounted(true);
  }, []);

  // Fetch user credits
  const fetchUserCredits = async () => {
    try {
      const response = await fetch('/api/credits');
      if (!response.ok) {
        throw new Error('Failed to fetch user credits');
      }
      const data = await response.json();
      setUserCredits(data.left_credits);
    } catch (error) {
      console.error('Error fetching user credits:', error);
      setUserCredits(0);
    }
  };

  // Check session status when component loads
  useEffect(() => {
    if (session) {
      fetchUserCredits();
    }
  }, [session]);

  // Refresh credits after successful processing
  useEffect(() => {
    if (processedImageUrl && session) {
      fetchUserCredits();
    }
  }, [processedImageUrl, session]);

  // Sync perception progress when real progress updates
  useEffect(() => {
    if (actualProcessingStarted && progress > perceptionProgress) {
      setPerceptionProgress(progress);
    }
  }, [progress, perceptionProgress, actualProcessingStarted]);

  // Enhanced perception progress simulation
  useEffect(() => {
    if (isProcessing) {
      const startTime = Date.now();

      const simulatedInterval = setInterval(() => {
        setPerceptionProgress(prev => {
          const elapsed = Date.now() - startTime;
          const waitingDuration = 15000; // 15 seconds waiting time

          let targetProgress;
          let increment;

          if (elapsed < waitingDuration) {
            // Preparation phase (0% → 40%, 15 seconds)
            const progressRatio = elapsed / waitingDuration;
            const sqrtProgress = Math.sqrt(progressRatio);
            targetProgress = Math.min(sqrtProgress * 40, 40);

            const timeDelta = 200;
            const nextProgressRatio = Math.min(1, (elapsed + timeDelta) / waitingDuration);
            const nextSqrtProgress = Math.sqrt(nextProgressRatio);
            const nextTargetProgress = Math.min(nextSqrtProgress * 40, 40);
            increment = Math.max(0.02, (nextTargetProgress - targetProgress) * 5);
          } else {
            // Processing phase (40% → 100%)
            if (!actualProcessingStarted) {
              targetProgress = 40;
              increment = 0;
            } else {
              const processingElapsed = elapsed - waitingDuration;
              const processingDuration = 25000; // Expected 25 seconds to complete
              const processingProgress = Math.min(processingElapsed / processingDuration, 1);
              targetProgress = 40 + (processingProgress * 60);
              increment = 0.05 * (200 / 1000);
            }

            if (progress > prev) {
              targetProgress = Math.max(prev, progress);
              increment = Math.max(increment, 0.5);
            } else {
              targetProgress = Math.min(prev + increment, 100);
            }
          }

          const newProgress = Math.min(prev + increment, targetProgress);
          return Math.min(Math.max(prev, newProgress), 100);
        });
      }, 200);

      return () => {
        clearInterval(simulatedInterval);
        setActualProcessingStarted(false);
      };
    }
  }, [isProcessing, progress, actualProcessingStarted]);

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select a valid image file');
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('File size must be less than 10MB');
      return;
    }

    setSelectedFile(file);

    // Create preview URL
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);

    // Clear previous results and reset progress
    setProcessedImageUrl('');
    setProgress(0);
    setPerceptionProgress(0);
    setActualProcessingStarted(false);
  };

  // Handle drag and drop
  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file) {
      const fakeEvent = {
        target: { files: [file] }
      } as unknown as React.ChangeEvent<HTMLInputElement>;
      handleFileSelect(fakeEvent);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  // Clear selected image and results
  const clearImage = () => {
    setSelectedFile(null);
    setPreviewUrl('');
    setProcessedImageUrl('');
    setProgress(0);
    setPerceptionProgress(0);
    setActualProcessingStarted(false);

    // Clear the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Upload image to R2 storage
  const uploadImageToR2 = async (file: File): Promise<string> => {
    setIsUploading(true);
    try {
      // Convert file to base64 for upload
      const reader = new FileReader();
      const base64Promise = new Promise<string>((resolve) => {
        reader.onload = () => {
          const result = reader.result as string;
          resolve(result);
        };
      });
      reader.readAsDataURL(file);
      const base64Data = await base64Promise;

      // Generate unique filename
      const fileExtension = file.name.split('.').pop()?.toLowerCase() || 'jpg';
      const filename = `bg-remove-input-${uuidv4()}.${fileExtension}`;

      // Upload to R2 via API
      const response = await fetch('/api/upload-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sourceUrl: base64Data,
          filename: filename,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const data = await response.json();
      return data.url;
    } finally {
      setIsUploading(false);
    }
  };

  // Remove background using Replicate API
  const removeBackground = async () => {
    if (!session?.user) {
      toast.error('Please sign in to remove backgrounds');
      router.push('/auth/signin');
      return;
    }

    if (!selectedFile) {
      toast.error('Please select an image first');
      return;
    }

    // Check if user has enough credits
    if (userCredits !== null && userCredits < 1) {
      toast.error('Insufficient credits. Each background removal requires 1 credit');
      router.push('/pricing');
      return;
    }

    // Reset progress states and start processing
    setProgress(0);
    setPerceptionProgress(0);
    setActualProcessingStarted(false);
    setProcessedImageUrl('');
    setIsProcessing(true);

    // Wait 15 seconds before starting actual processing
    setTimeout(() => {
      performActualProcessing();
    }, 15000);
  };

  // Actual background removal processing function
  const performActualProcessing = async () => {
    setActualProcessingStarted(true);
    setProgress(40); // Start real progress from 40%

    try {
      const taskId = `bg_remove_${uuidv4()}`;

      // Step 1: Deduct credits first
      setProgress(45);
      try {
        const deductResponse = await fetch('/api/deduct-credits', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            credits: 1, // Deduct 1 credit for background removal
            taskId: taskId,
          }),
        });

        if (!deductResponse.ok) {
          const errorData = await deductResponse.json();
          if (errorData.error === "insufficient_credits") {
            router.push('/pricing');
            return;
          }
          throw new Error(errorData.error || 'Failed to deduct credits');
        }

        const creditData = await deductResponse.json();
        setUserCredits(creditData.creditsLeft);
        setProgress(50);
      } catch (creditError) {
        console.error('Failed to deduct credits:', creditError);
        setIsProcessing(false);
        return;
      }

      // Step 2: Upload image
      setProgress(55);
      const uploadedImageUrl = await uploadImageToR2(selectedFile!);
      setProgress(65);

      // Step 3: Remove background using Replicate API
      setProgress(70);

      const response = await fetch('/api/background-remover', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl: uploadedImageUrl,
          taskId
        }),
      });

      setProgress(85);

      if (!response.ok) {
        const errorData = await response.json();
        // Refund credits on failure
        await refundCredits(taskId);
        throw new Error(errorData.error || 'Failed to remove background');
      }

      const data = await response.json();
      setProgress(95);

      if (!data.imageUrl) {
        await refundCredits(taskId);
        throw new Error('No image URL received');
      }

      console.log('Processed image URL:', data.imageUrl);
      setProcessedImageUrl(data.imageUrl);
      setProgress(100);
      toast.success('Background removed successfully!');

    } catch (error) {
      console.error('Processing error:', error);
      setProgress(0);
      setPerceptionProgress(0);
      toast.error('Failed to remove background. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  // Refund credits function
  const refundCredits = async (taskId: string) => {
    try {
      const response = await fetch('/api/refund-credits', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          credits: 1,
          taskId: taskId,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setUserCredits(data.creditsLeft);
        console.log('Processing failed, 1 credit has been automatically refunded');
      }
    } catch (error) {
      console.error('Failed to refund credits:', error);
    }
  };

  // Download processed image
  const downloadImage = async () => {
    if (!processedImageUrl) return;

    try {
      const proxyUrl = `/api/proxy-image?url=${encodeURIComponent(processedImageUrl)}`;
      const link = document.createElement('a');
      link.href = proxyUrl;
      link.download = 'background-removed.png';
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success('Download started successfully!');
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Failed to download image');
    }
  };

  // Open image in new window - directly open the original image URL
  const openImageInNewWindow = () => {
    if (!processedImageUrl) return;

    try {
      // Open the original image URL directly in new window
      window.open(processedImageUrl, '_blank', 'noopener,noreferrer');
    } catch (error) {
      console.error('Error opening image:', error);
      toast.error('Failed to open image');
    }
  };

  return (
    <div className="w-full max-w-6xl mx-auto py-4 md:py-6 space-y-6 md:space-y-8">
      <div className="text-center">
        <h2 className="text-2xl md:text-3xl font-bold mb-3 md:mb-4">{title}</h2>
        <p className="text-base md:text-lg text-muted-foreground">
          Remove background from image instantly and create transparent background with AI
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6 lg:gap-8">
        {/* Input Section */}
        <Card>
          <CardContent className="p-3 sm:p-4 md:p-6 space-y-4 md:space-y-6">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
              <h3 className="text-lg md:text-xl font-semibold">Upload Your Image</h3>
              <div className="bg-blue-100 text-blue-600 px-2 md:px-3 py-1 rounded-full flex items-center gap-1 md:gap-2 self-start sm:self-auto">
                <span className="text-xs md:text-sm font-normal">Credits:</span>{" "}
                {mounted && session ? (
                  <span className="text-sm md:text-lg font-bold">{userCredits !== null ? userCredits : "Loading..."}</span>
                ) : (
                  <span className="text-sm md:text-lg font-bold">
                    <a href="/auth/signin" className="hover:underline">Sign in</a>
                  </span>
                )}
                <svg
                  className="w-3 h-3 md:w-4 md:h-4 text-amber-500 fill-current"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                >
                  <path d="M12 17a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm0-15c-4.97 0-9 4.03-9 9v7c0 1.66 1.34 3 3 3h12c1.66 0 3-1.34 3-3v-7c0-4.97-4.03-9-9-9zm0 3c3.31 0 6 2.69 6 6v7c0 .55-.45 1-1 1H7c-.55 0-1-.45-1-1v-7c0-3.31 2.69-6 6-6z" />
                </svg>
              </div>
            </div>

            {/* File Upload Area */}
            <div
              className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-3 sm:p-4 md:p-8 text-center cursor-pointer hover:border-muted-foreground/50 transition-colors"
              onClick={() => fileInputRef.current?.click()}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
            >
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileSelect}
                className="hidden"
              />

              {previewUrl ? (
                <div className="space-y-3 md:space-y-4">
                  <div className="relative w-32 h-32 sm:w-40 sm:h-40 md:w-48 md:h-48 mx-auto">
                    <Image
                      src={previewUrl}
                      alt="Preview"
                      fill
                      className="object-cover rounded-lg"
                    />
                    {/* Clear image button */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        clearImage();
                      }}
                      className="absolute -top-1 -right-1 md:-top-2 md:-right-2 w-5 h-5 md:w-6 md:h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center shadow-lg transition-colors"
                      title="Remove image"
                    >
                      <X className="w-3 h-3 md:w-4 md:h-4" />
                    </button>
                  </div>
                  <p className="text-xs md:text-sm text-muted-foreground px-2">
                    Click to change image or use the × button to remove
                  </p>
                </div>
              ) : (
                <div className="space-y-3 md:space-y-4">
                  <Upload className="w-8 h-8 md:w-12 md:h-12 mx-auto text-muted-foreground" />
                  <div>
                    <p className="text-base md:text-lg font-medium">Drop your image here</p>
                    <p className="text-xs md:text-sm text-muted-foreground">
                      or click to browse (max 10MB)
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Processing Info */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 md:p-4">
              <div className="flex items-center gap-2 text-blue-700">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <span className="text-sm font-medium">Output: PNG with transparent background</span>
              </div>
            </div>

            {/* Remove Background Button */}
            <Button
              onClick={removeBackground}
              disabled={!selectedFile || isProcessing || isUploading}
              className="w-full text-sm md:text-base"
              size="lg"
            >
              {isProcessing ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {perceptionProgress > 0 ? `Removing Background... ${Math.round(perceptionProgress)}%` : `Removing Background...`}
                </>
              ) : isUploading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                'Remove Background Free (1 Credit)'
              )}
            </Button>

            {/* Progress Bar */}
            {isProcessing && (
              <>
                <div className="w-full bg-muted rounded-full h-2 md:h-2.5 mt-2 overflow-hidden">
                  <div
                    className="bg-blue-600 h-2 md:h-2.5 rounded-full transition-all duration-300"
                    style={{ width: `${perceptionProgress}%` }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-[shimmer_2s_infinite]"></div>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground text-center mt-1">
                  Processing with AI...
                </p>
                <p className="text-xs md:text-sm text-red-600 text-center mt-2 font-medium px-2">
                  Your background is being removed. This typically takes 30s-1 minute. Please don't close this page.
                </p>
              </>
            )}
          </CardContent>
        </Card>

        {/* Result Section */}
        <Card>
          <CardContent className="p-3 sm:p-4 md:p-6 space-y-4 md:space-y-6">
            <h3 className="text-lg md:text-xl font-semibold">Transparent Background Result</h3>

            <div className="relative aspect-square w-full bg-muted rounded-lg overflow-hidden">
              {processedImageUrl ? (
                <div
                  className="relative w-full h-full cursor-pointer group"
                  onClick={openImageInNewWindow}
                  title="Click to view full image in new window"
                >
                  <Image
                    src={processedImageUrl}
                    alt="Background removed image"
                    fill
                    className="object-cover transition-transform group-hover:scale-105"
                    priority
                    unoptimized={typeof processedImageUrl === 'string' && (processedImageUrl.includes('replicate.delivery') || processedImageUrl.includes('pbxt.replicate.delivery'))}
                    onError={() => {
                      console.error('Image failed to load:', processedImageUrl);
                    }}
                    onLoad={() => {
                      console.log('Image loaded successfully:', processedImageUrl);
                    }}
                  />
                  {/* Hover overlay */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white bg-opacity-90 rounded-full p-2">
                      <ExternalLink className="w-5 h-5 text-gray-700" />
                    </div>
                  </div>
                </div>
              ) : (
                <div className="absolute inset-0 flex items-center justify-center p-4">
                  <p className="text-muted-foreground text-center text-sm md:text-base">
                    {isProcessing ? 'Processing with AI...' : 'Your image with transparent background will appear here'}
                  </p>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            {processedImageUrl && (
              <div className="space-y-3">
                {/* View Full Image Button */}
                <Button
                  onClick={openImageInNewWindow}
                  className="w-full text-sm md:text-base"
                  size="lg"
                  variant="default"
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  <span className="hidden sm:inline">View Full Image</span>
                  <span className="sm:hidden">View Image</span>
                </Button>

                {/* Download Button */}
                <Button
                  onClick={downloadImage}
                  className="w-full text-sm md:text-base"
                  size="lg"
                  variant="outline"
                >
                  <Download className="w-4 h-4 mr-2" />
                  <span className="hidden sm:inline">Download Transparent Image</span>
                  <span className="sm:hidden">Download Image</span>
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
