<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Progress Bar Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .progress-container {
            margin: 20px 0;
        }
        .progress-bar {
            width: 100%;
            height: 10px;
            background-color: #e0e0e0;
            border-radius: 5px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background-color: #2563eb;
            border-radius: 5px;
            transition: width 0.3s ease;
            position: relative;
        }
        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        .progress-text {
            text-align: center;
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }
        .warning-text {
            text-align: center;
            margin-top: 10px;
            font-size: 12px;
            color: #dc2626;
            font-weight: 500;
        }
        .button {
            background-color: #2563eb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
        }
        .button:disabled {
            background-color: #9ca3af;
            cursor: not-allowed;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 6px;
            background-color: #f3f4f6;
        }
    </style>
</head>
<body>
    <h1>感知进度条测试</h1>
    <p>🚀 新的感知进度条策略：</p>
    <ul>
        <li><strong>准备阶段 (0% → 60%, 40秒)</strong>：使用平方根函数实现非线性增长</li>
        <li>前10秒：快速到达~40%，给用户立即的正反馈</li>
        <li>中20秒：稳定增长到~55%，保持用户信心</li>
        <li>后10秒：缓慢到达60%，为真实处理做准备</li>
        <li><strong>生成阶段 (60% → 100%)</strong>：使用分段线性增长策略</li>
        <li>60-65%: 1.2%/秒，65-70%: 0.8%/秒，70-80%: 0.5%/秒</li>
        <li>80-85%: 0.3%/秒，85-90%: 0.2%/秒，90-95%: 0.1%/秒，95%+: 0.05%/秒</li>
    </ul>

    <div class="progress-container">
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill" style="width: 0%"></div>
        </div>
        <div class="progress-text" id="progressText">Ready to start</div>
        <div class="warning-text" id="warningText" style="display: none;">
            Your image is being generated. This typically takes 20s-2 minutes. Please don't close this page.
        </div>
    </div>

    <button class="button" id="startButton" onclick="startProgress()">开始生成</button>

    <div class="status" id="status">
        <strong>状态：</strong><span id="statusText">等待开始</span><br>
        <strong>阶段：</strong><span id="phaseText">未开始</span><br>
        <strong>时间：</strong><span id="timeText">0s</span>
    </div>

    <script>
        let startTime = null;
        let progressInterval = null;
        let currentProgress = 0;
        let actualProcessingStarted = false;

        function startProgress() {
            const button = document.getElementById('startButton');
            const statusText = document.getElementById('statusText');
            const warningText = document.getElementById('warningText');

            button.disabled = true;
            button.textContent = '生成中...';
            statusText.textContent = '进行中';
            warningText.style.display = 'block';

            startTime = Date.now();
            actualProcessingStarted = false;
            currentProgress = 0;

            progressInterval = setInterval(updateProgress, 150);

            // 模拟40秒后开始"实际处理"
            setTimeout(() => {
                actualProcessingStarted = true;
                document.getElementById('phaseText').textContent = '实际处理阶段';
            }, 40000);
        }

        function updateProgress() {
            const elapsed = Date.now() - startTime;
            const waitingDuration = 40000; // 40秒等待

            let targetProgress;
            let increment;
            let phaseText;

            if (elapsed < waitingDuration) {
                // 🚀 准备阶段 (0% → 60%, 40秒) - 使用平方根函数实现非线性增长
                const progressRatio = elapsed / waitingDuration; // 0 到 1
                const sqrtProgress = Math.sqrt(progressRatio); // 平方根函数，前期快后期慢
                targetProgress = Math.min(sqrtProgress * 60, 60); // 映射到 0-60%

                // 动态计算增量，确保平滑过渡
                const timeDelta = 200; // 200ms更新间隔
                const nextProgressRatio = Math.min(1, (elapsed + timeDelta) / waitingDuration);
                const nextSqrtProgress = Math.sqrt(nextProgressRatio);
                const nextTargetProgress = Math.min(nextSqrtProgress * 60, 60);
                increment = Math.max(0.02, (nextTargetProgress - targetProgress) * 5); // 最小增量0.02%

                if (elapsed < 10000) {
                    phaseText = '快速启动阶段 (~40%)';
                } else if (elapsed < 30000) {
                    phaseText = '稳定处理阶段 (~55%)';
                } else {
                    phaseText = '准备完成阶段 (→60%)';
                }
            } else {
                // 🧠 生成阶段 (60% → 100%) - 使用分段线性增长策略
                if (currentProgress < 65) {
                    // 60-65%: 1.2%/秒 (快速，继续正反馈)
                    increment = 1.2 * (200 / 1000); // 200ms间隔转换为秒
                    phaseText = '快速生成阶段 (60-65%)';
                } else if (currentProgress < 70) {
                    // 65-70%: 0.8%/秒 (中等速度)
                    increment = 0.8 * (200 / 1000);
                    phaseText = '中速生成阶段 (65-70%)';
                } else if (currentProgress < 80) {
                    // 70-80%: 0.5%/秒 (开始放慢)
                    increment = 0.5 * (200 / 1000);
                    phaseText = '稳定生成阶段 (70-80%)';
                } else if (currentProgress < 85) {
                    // 80-85%: 0.3%/秒 (更慢)
                    increment = 0.3 * (200 / 1000);
                    phaseText = '精细处理阶段 (80-85%)';
                } else if (currentProgress < 90) {
                    // 85-90%: 0.2%/秒 (很慢)
                    increment = 0.2 * (200 / 1000);
                    phaseText = '优化阶段 (85-90%)';
                } else if (currentProgress < 95) {
                    // 90-95%: 0.1%/秒 (非常慢)
                    increment = 0.1 * (200 / 1000);
                    phaseText = '最终调整阶段 (90-95%)';
                } else {
                    // 95%+: 0.05%/秒 (极慢，等待真实完成)
                    increment = 0.05 * (200 / 1000);
                    phaseText = '完成阶段 (95%+)';
                }

                targetProgress = Math.min(currentProgress + increment, 100);
            }

            // 计算新进度，确保只增不减
            const newProgress = Math.min(Math.max(currentProgress, currentProgress + increment), targetProgress);
            currentProgress = Math.min(newProgress, 100);

            // 更新UI
            document.getElementById('progressFill').style.width = currentProgress + '%';
            document.getElementById('progressText').textContent = 'Generating...';
            document.getElementById('phaseText').textContent = phaseText;
            document.getElementById('timeText').textContent = Math.round(elapsed / 1000) + 's';

            // 检查是否完成
            if (currentProgress >= 100) {
                clearInterval(progressInterval);
                document.getElementById('startButton').disabled = false;
                document.getElementById('startButton').textContent = '重新开始';
                document.getElementById('statusText').textContent = '已完成';
                document.getElementById('phaseText').textContent = '完成';
                document.getElementById('warningText').style.display = 'none';
            }
        }

        function getProgressText(progress) {
            if (progress < 25) return "Initializing...";
            if (progress < 50) return "Analyzing image...";
            if (progress < 75) return "Preparing generation...";
            if (progress < 85) return "Processing request...";
            if (progress < 95) return "Generating image...";
            return "Finalizing...";
        }
    </script>
</body>
</html>
