import Branding from "@/components/blocks/branding";
import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import Feature3 from "@/components/blocks/feature3";
import Hero from "@/components/blocks/hero";
import HeroWithCompare from "@/components/blocks/hero-with-compare";
import ImageCompareGallery from "@/components/blocks/image-compare-gallery";
import Showcase from "@/components/blocks/showcase";
import Stats from "@/components/blocks/stats";
import Testimonial from "@/components/blocks/testimonial";
import { getLandingPage } from "@/services/page";
import KontextDev from "@/components/kontextdev";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export const runtime = "edge";

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}) {
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/photo-restoration`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/photo-restoration`;
  }

  return {
    title: "Photo Restoration - AI Restore Old Photos Free | Kontext Dev",
    description: "Restore old photos with AI photo restoration technology. Fix damaged photos, restore faded images online free. Professional photo restoration services.",
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function PhotoRestorationPage({
  params: { locale },
}: {
  params: { locale: string };
}) {
  const page = await getLandingPage(locale);

  // Photo restoration example comparison images
  const compareImages = {
    originalSrc: "https://pic.kontext-dev.com/mother-daughter-damaged-photo-before-restoration.webp",
    modifiedSrc: "https://pic.kontext-dev.com/mother-daughter-photo-restored-after-repair.webp",
    beforeText: "Old damaged photo",
    afterText: "Restored photo"
  };

  // More photo restoration examples
  const compareGroups = [
    {
      id: 1,
      originalSrc: "https://pic.kontext-dev.com/family-damaged-photo-before-restoration.webp",
      modifiedSrc: "https://pic.kontext-dev.com/family-photo-ai-restored.webp",
      alt: "Restore old photos with AI photo restoration",
      beforeText: "Faded old photo",
      afterText: "AI restored photo"
    },
    {
      id: 2,
      originalSrc: "https://pic.kontext-dev.com/sister-damaged-photo-before-restoration.webp",
      modifiedSrc: "https://pic.kontext-dev.com/sister-photo-ai-restored.webp",
      alt: "Fix old photos and restore damaged images",
      beforeText: "Damaged vintage photo",
      afterText: "Restored vintage photo"
    },
    {
      id: 3,
      originalSrc: "https://pic.kontext-dev.com/albert-einstein-old-photo.webp",
      modifiedSrc: "https://pic.kontext-dev.com/albert-einstein-photo-restoration.webp",
      alt: "Restore old photographs and enhance quality",
      beforeText: "Old black & white photo",
      afterText: "Enhanced photo"
    },
    {
      id: 4,
      originalSrc: "https://pic.kontext-dev.com/woman-damaged-photo-before-restoration.webp",
      modifiedSrc: "https://pic.kontext-dev.com/woman-photo-ai-restored.webp",
      alt: "Photo repair and restoration services",
      beforeText: "Torn family photo",
      afterText: "Repaired family photo"
    }
  ];

  return (
    <>
      

      {/* Hero section with comparison */}
      {page.hero && <HeroWithCompare
        hero={{
          ...page.hero,
          title: "Photo Restoration - AI Restore Old Photos Online Free",
          description: "Restore old photos with our advanced AI photo restoration technology. Fix damaged photos, restore faded images, and bring old photographs back to life online free. Our photo restoration services use artificial intelligence to repair old photos while preserving precious memories.",
          buttons: [
            {
              ...page.hero.buttons?.[0],
              title: "Restore Photos Now",
              url: "#photo-restoration"
            },
            {
              ...page.hero.buttons?.[1],
              title: "View Examples",
              url: "#examples"
            }
          ],
          show_happy_users: false
        }}
        compareImages={compareImages}
      />}

      {/* Photo restoration tool section */}
      <div id="photo-restoration" className="text-center py-8">
        <h2 className="text-3xl font-bold">AI Photo Restoration Tool</h2>
        <p className="text-xl text-muted-foreground mt-2 mb-8">
          Upload your old photos and our AI photo restoration technology will automatically restore old images and fix damaged photos
        </p>
      </div>

      <KontextDev
        title="Photo Restoration"
        showPromptInput={false}
        showTranslation={false}
        showQuickActions={true}
        showQuickStyleOptions={false}
        enableRestoreNavigation={false}
        photoRestorationMode={true}
        defaultPrompt=""
        generateButtonText="Restore Photo"
        resultTitle="Photo Restoration Result"
        disableTranslation={true}
      />

      {/* Photo restoration gallery */}
      <div id="examples">
        <ImageCompareGallery
          title="AI Photo Restoration Examples"
          description="See how our advanced photo restoration services restore old photos, fix damaged images, and repair old photographs with professional AI photo restoration results"
          compareGroups={compareGroups}
        />
      </div>

      {/* Features introduction */}
      {page.introduce && <Feature1 section={{
        ...page.introduce,
        title: "Why Choose Our AI Photo Restoration Services",
        description: "Our AI photo restoration technology provides advanced solutions to restore old photos and fix damaged images with professional quality results",
        image: {
          src: "https://pic.kontext-dev.com/photo-restoration-before-after.webp",
          alt: "Photo restoration features"
        },
        items: [
          {
            title: "Smart Damage Detection",
            description: "Our AI photo restoration uses advanced algorithms to automatically detect damaged areas in old photos, including scratches, tears, and fading",
            icon: "RiSearchLine"
          },
          {
            title: "Original Quality Preservation",
            description: "Our photo restoration services employ cutting-edge AI to restore old images while maintaining original photo quality and preserving important details",
            icon: "RiImageLine"
          },
          {
            title: "Multiple Damage Types Support",
            description: "Our photo restoration technology effectively handles various damage types including faded photos, torn images, water damage, and color restoration",
            icon: "RiImageEditLine"
          },
          {
            title: "Free Photo Restoration",
            description: "Our free photo restoration service lets you start restoring old photos right away with no hidden fees",
            icon: "RiVipCrownLine"
          }
        ]
      }} />}

      {/* Benefits section */}
      {page.benefit && <Feature2 section={{
        ...page.benefit,
        title: "Benefits of Using Our Photo Restoration Services",
        description: "Our AI photo restoration offers several advantages for restoring old photos and preserving precious memories",
        items: [
          {
            title: "Time-Efficient Processing",
            description: "Restore old photos in seconds instead of spending hours with manual photo repair techniques",
            icon: "RiTimeLine",
            image: {
              src: "https://pic.kontext-dev.com/photo-restoration-time-efficient.webp",
              alt: "Time-Efficient Photo Restoration"
            }
          },
          {
            title: "Professional-Grade Results",
            description: "Get high-quality restored photos suitable for printing, framing, and digital preservation of family memories",
            icon: "RiMedalLine",
            image: {
              src: "https://pic.kontext-dev.com/professional-photo-restoration-results.webp",
              alt: "Professional-Grade Photo Restoration Results"
            }
          },
          {
            title: "User-Friendly Interface",
            description: "Simple interface designed for users of all skill levels - restore old images free of technical complexity",
            icon: "RiUserSmileLine",
            image: {
              src: "https://pic.kontext-dev.com/family-photo-restoration-before-after.webp",
              alt: "Family photo before and after using Photo Restoration by Kontext Dev, showing AI restoration result"
            }
          },
          {
            title: "Data Privacy Protection",
            description: "Your precious old photos are processed securely and not stored after photo restoration is complete",
            icon: "RiShieldLine",
            image: {
              src: "https://pic.kontext-dev.com/vintage-photo-restoration-privacy.webp",
              alt: "Vintage photo restoration with privacy protection by Kontext Dev"
            }
          }
        ]
      }} />}

      {/* Usage steps */}
      {page.usage && <Feature3 section={{
        ...page.usage,
        title: "How to Restore Old Photos with Our AI Photo Restoration",
        description: "Restore old photos and fix damaged images in just four simple steps with our AI photo restoration technology",
        items: [
          {
            title: "Upload Your Old Photo",
            description: "Select and upload the old photo or damaged image you want to restore",
            icon: "RiUploadLine"
          },
          {
            title: "AI Damage Analysis",
            description: "Our AI photo restoration will automatically detect and analyze damaged areas for optimal photo repair",
            icon: "RiScanLine"
          },
          {
            title: "Preview Restored Result",
            description: "Check the preview to ensure old photos are properly restored while preserving original details",
            icon: "RiEyeLine"
          },
          {
            title: "Download Restored Photo",
            description: "Download your high-quality restored photo ready for printing or digital preservation",
            icon: "RiDownloadLine"
          }
        ]
      }} />}

      {/* Key Features section */}
      {page.feature && <Feature section={{
        ...page.feature,
        title: "Key Features of Our AI Photo Restoration",
        description: "Discover the powerful capabilities that make our photo restoration services stand out from other photo repair solutions",
        items: [
          {
            title: "Advanced AI Algorithm",
            description: "Our AI photo restoration utilizes state-of-the-art artificial intelligence to intelligently restore old photos and fix damaged images while preserving original photo quality.",
            icon: "RiAiGenerate"
          },
          {
            title: "Multi-Type Damage Support",
            description: "Our photo restoration services can handle various damage types including faded photos, torn images, water damage, scratches, and color restoration with excellent results.",
            icon: "RiImageEditLine"
          },
          {
            title: "High Resolution Processing",
            description: "Our photo restoration technology processes high-resolution old photos without quality loss, ensuring your restored images maintain their clarity and detail.",
            icon: "RiImageLine"
          },
          {
            title: "Intelligent Image Reconstruction",
            description: "With our AI photo restoration, the technology intelligently reconstructs damaged areas of old photographs, ensuring natural and seamless restoration results.",
            icon: "RiPaintBrushLine"
          },
          {
            title: "Fast Processing Speed",
            description: "Our photo restoration services quickly restore old photos with optimized algorithms that deliver professional results in seconds, saving you valuable time.",
            icon: "RiTimerFlashLine"
          },
          {
            title: "Cloud-Based Solution",
            description: "Our online photo restoration operates in the cloud, so there's no need to download photo restoration software or worry about your device's processing power.",
            icon: "RiCloudLine"
          }
        ]
      }} />}

      {/* Stats section - REMOVED */}

      {/* Pricing Section */}
      <div className="container py-12 text-center">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl font-bold tracking-tight mb-4">Need More Advanced Photo Restoration Features?</h2>
          <p className="text-xl text-muted-foreground mb-8">
            Upgrade to our premium photo restoration services for batch processing, higher resolution support, and priority processing for your old photo restoration projects
          </p>
          <Link href="/pricing">
            <Button size="lg" className="gap-2">
              View Premium Photo Restoration Plans
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M5 12h14" />
                <path d="m12 5 7 7-7 7" />
              </svg>
            </Button>
          </Link>
        </div>
      </div>

      {/* Testimonials */}
      {page.testimonial && <Testimonial section={{
        ...page.testimonial,
        title: "What Users Say About Our Photo Restoration Services",
        description: "Hear from families and professionals who use our AI photo restoration to restore old photos and preserve precious memories",
        items: [
          {
            description: "This photo restoration service saved my family's precious memories. I had a collection of old damaged photos from my grandmother, and the AI restored them perfectly. The old photo restoration results exceeded my expectations.",
            title: "Anna P.",
            label: "Memory Curator",
            image: {
              src: "https://pic.kontext-dev.com/7.webp",
              alt: "Anna P., Memory Curator"
            }
          },
          {
            description: "I was skeptical about using AI photo restoration, but the results are impressive. My faded old photographs were restored without any artifacts, and the image quality remained excellent. Best photo restoration service I've used.",
            title: "Clara H.",
            label: "Family Archivist",
            image: {
              src: "https://pic.kontext-dev.com/8.webp",
              alt: "Clara H., Family Archivist"
            }
          },
          {
            description: "As a genealogist, I often need to restore old photos for family trees. This AI photo restoration makes it quick and easy to fix old photos, with professional results every time.",
            title: "James L.",
            label: "Visual Storyteller",
            image: {
              src: "https://pic.kontext-dev.com/9.webp",
              alt: "James L., Visual Storyteller"
            }
          },
          {
            description: "I run a photo restoration business and this AI photo restoration tool has become essential for our workflow. It helps us restore old images efficiently while maintaining professional quality standards.",
            title: "Mark V.",
            label: "Photo Restoration Professional",
            image: {
              src: "https://pic.kontext-dev.com/10.webp",
              alt: "Mark V., Photo Restoration Professional"
            }
          },
          {
            description: "The ability to restore old photos without losing quality has been a game changer for my historical research. This free photo restoration service is highly recommended for anyone working with vintage photographs.",
            title: "Olivia W.",
            label: "Cultural Historian",
            image: {
              src: "https://pic.kontext-dev.com/11.webp",
              alt: "Olivia W., Cultural Historian"
            }
          },
          {
            description: "I've tried several photo restoration software options, but this AI photo restoration stands out for its ease of use and quality results. It handles even severely damaged old photographs with advanced restoration technology.",
            title: "Henry C.",
            label: "Preservation Specialist",
            image: {
              src: "https://pic.kontext-dev.com/12.webp",
              alt: "Henry C., Preservation Specialist"
            }
          }
        ]
      }} />}

      {/* FAQ section */}
      {page.faq && <FAQ section={{
        ...page.faq,
        title: "FAQ About Photo Restoration Services",
        description: "Common questions about our AI photo restoration technology and how to restore old photos effectively",
        items: [
          {
            title: "Is this photo restoration service really free to use?",
            description: "Yes, our basic photo restoration features are completely free to use. You receive 10 free credits upon signing up to start restoring old photos right away. For more advanced photo restoration needs, we also offer premium features in our paid version."
          },
          {
            title: "What types of damage can your AI photo restoration handle?",
            description: "Our AI photo restoration can handle most common damage types, including faded photos, torn images, scratches, water damage, color restoration, and old photograph enhancement. The photo restoration technology is effective regardless of the damage location on the image."
          },
          {
            title: "How is the image quality after using your photo restoration services?",
            description: "We use advanced AI photo restoration technology to maintain and enhance original image quality while fixing damaged areas. Our algorithms are designed to preserve important details and restore old photos naturally. Results may vary depending on damage severity and original photo quality."
          },
          {
            title: "Are there file size or image count limitations for the free photo restoration?",
            description: "The free photo restoration version has certain limitations: maximum file size of 10MB and each photo restoration uses 5 credits. Currently, our photo restoration service supports processing one old photo at a time."
          },
          {
            title: "How long does it take to restore old photos using your AI photo restoration?",
            description: "Most old photos are restored within 10-30 seconds using our AI photo restoration, depending on the complexity of the damage and the image size. Our cloud-based photo restoration ensures fast results without taxing your device."
          },
          {
            title: "Can I use the restored photos commercially after using your photo restoration services?",
            description: "Yes, you can use your restored photos commercially as long as you own the original photographs. Our photo restoration service simply repairs and enhances your existing images. Always ensure you have proper rights to the original photos before commercial use."
          },
          {
            title: "What kind of old photos work best with the photo restoration technology?",
            description: "For optimal photo restoration results, we recommend using clear scans or photos of your old photographs. Our AI photo restoration works best with JPEG, PNG, and WebP formats, and can process vintage photos, family photographs, historical images, and other old pictures effectively."
          },
          {
            title: "Is my data safe when I use the photo restoration service?",
            description: "We take data privacy very seriously. The old photos you upload to our photo restoration service are used only for restoration processing and are not stored on our servers after the process is complete. We adhere to strict privacy policies to ensure the security of your precious memories."
          }
        ]
      }} />}

      {/* Call to action */}
      {page.cta && <CTA section={{
        ...page.cta,
        title: "Try Our Photo Restoration Service Now",
        description: "Restore old photos easily with our AI-powered photo restoration technology - get professional results in seconds and preserve your precious memories",
        buttons: [
          {
            ...page.cta.buttons?.[0],
            title: "Start Restoring Photos",
            url: "#photo-restoration"
          }
        ]
      }} />}
    </>
  );
} 