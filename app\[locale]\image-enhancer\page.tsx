import Branding from "@/components/blocks/branding";
import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import Feature3 from "@/components/blocks/feature3";
import Hero from "@/components/blocks/hero";
import HeroWithCompare from "@/components/blocks/hero-with-compare";
import ImageCompareGallery from "@/components/blocks/image-compare-gallery";
import Showcase from "@/components/blocks/showcase";
import Stats from "@/components/blocks/stats";
import Testimonial from "@/components/blocks/testimonial";
import { getLandingPage } from "@/services/page";
import KontextDev from "@/components/kontextdev";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export const runtime = "edge";

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}) {
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/image-enhancer`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/image-enhancer`;
  }

  return {
    title: "Image Enhancer - AI Photo Enhancer Free Online",
    description: "Enhance image quality with our AI image enhancer. Free photo enhancer to improve picture quality and sharpen images online instantly.",
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function ImageEnhancerPage({
  params: { locale },
}: {
  params: { locale: string };
}) {
  const page = await getLandingPage(locale);

  // Image enhancer example comparison images
  const compareImages = {
    originalSrc: "https://pic.kontext-dev.com/blurry-children-photo.jpg",
    modifiedSrc: "https://pic.kontext-dev.com/enhanced-kids-photo.webp",
    beforeText: "Original image",
    afterText: "Enhanced image"
  };

  // More image enhancement examples
  const compareGroups = [
    {
      id: 1,
      originalSrc: "https://pic.kontext-dev.com/blurry-elderly-couple-picture.jpg",
      modifiedSrc: "https://pic.kontext-dev.com/enhanced-elderly-couple-picture.webp",
      alt: "AI image enhancer improving picture quality",
      beforeText: "Low quality image",
      afterText: "AI enhanced image"
    },
    {
      id: 2,
      originalSrc: "https://pic.kontext-dev.com/blurry-rain-umbrella-woman.jpg",
      modifiedSrc: "https://pic.kontext-dev.com/enhanced-rain-umbrella-woman.webp",
      alt: "Photo enhancer improving image resolution",
      beforeText: "Blurry photo",
      afterText: "Sharpened image"
    },
    {
      id: 3,
      originalSrc: "https://pic.kontext-dev.com/blurry-bird-photo.jpg",
      modifiedSrc: "https://pic.kontext-dev.com/enhanced-bird-photo.webp",
      alt: "Picture enhancer for image quality enhancement",
      beforeText: "Original photo",
      afterText: "Enhanced quality"
    },
    {
      id: 4,
      originalSrc: "https://pic.kontext-dev.com/blurry-hat-camera-man-original.jpg",
      modifiedSrc: "https://pic.kontext-dev.com/enhanced-hat-camera-man-original.webp",
      alt: "AI photo enhancer for better image quality",
      beforeText: "Standard quality",
      afterText: "Enhanced quality"
    }
  ];

  return (
    <>
      

      {/* Hero section with comparison */}
      {page.hero && <HeroWithCompare
        hero={{
          ...page.hero,
          title: "Image Enhancer - AI Photo Enhancer Free Online",
          description: "Enhance image quality with our advanced AI image enhancer technology. Our photo enhancer improves picture quality, sharpens images, and enhances photos online free. Use our AI photo enhancer to improve resolution of image and get professional image enhancement results.",
          buttons: [
            {
              ...page.hero.buttons?.[0],
              title: "Enhance Images Now",
              url: "#image-enhancer"
            },
            {
              ...page.hero.buttons?.[1],
              title: "View Examples",
              url: "#examples"
            }
          ],
          show_happy_users: false
        }}
        compareImages={compareImages}
      />}

      {/* Image enhancer tool section */}
      <div id="image-enhancer" className="text-center py-8">
        <h2 className="text-3xl font-bold">AI Image Enhancer Tool</h2>
        <p className="text-xl text-muted-foreground mt-2 mb-8">
          Upload your images and our AI image enhancer will automatically enhance image quality and improve picture quality with advanced photo enhancer technology
        </p>
      </div>

      <KontextDev
        title="Image Enhancer"
        showPromptInput={false}
        showTranslation={false}
        showQuickActions={true}
        showQuickStyleOptions={false}
        enableRestoreNavigation={false}
        photoRestorationMode={true}
        defaultPrompt=""
        generateButtonText="Enhance Image"
        resultTitle="Image Enhancement Result"
        disableTranslation={true}
      />

      {/* Image enhancement gallery */}
      <div id="examples">
        <ImageCompareGallery
          title="AI Image Enhancer Examples"
          description="See how our advanced AI image enhancer improves picture quality, enhances image quality, and delivers professional photo enhancer results with cutting-edge image enhancement technology"
          compareGroups={compareGroups}
        />
      </div>

      {/* Features introduction */}
      {page.introduce && <Feature1 section={{
        ...page.introduce,
        title: "Why Choose Our AI Image Enhancer Services",
        description: "Our AI image enhancer technology provides advanced solutions to enhance image quality and improve picture quality with professional photo enhancer results",
        image: {
          src: "https://pic.kontext-dev.com/why-choose-our-ai-image-enhancer-services.webp",
          alt: "Image enhancer features"
        },
        items: [
          {
            title: "Smart Quality Detection",
            description: "Our AI image enhancer uses advanced algorithms to automatically analyze image quality and identify areas that need enhancement for optimal picture quality",
            icon: "RiSearchLine"
          },
          {
            title: "Original Detail Preservation",
            description: "Our photo enhancer employs cutting-edge AI to enhance image quality while maintaining original details and natural appearance in your pictures",
            icon: "RiImageLine"
          },
          {
            title: "Multiple Enhancement Types",
            description: "Our image enhancer technology effectively handles various enhancement needs including sharpening images, improving resolution of image, and overall picture quality enhancement",
            icon: "RiImageEditLine"
          },
          {
            title: "Image Enhancer Free",
            description: "Our image enhancer free service lets you start enhancing image quality right away with no hidden fees using our AI photo enhancer",
            icon: "RiVipCrownLine"
          }
        ]
      }} />}

      {/* Benefits section */}
      {page.benefit && <Feature2 section={{
        ...page.benefit,
        title: "Benefits of Using Our Image Enhancer Services",
        description: "Our AI image enhancer offers several advantages for enhancing image quality and improving picture quality with professional results",
        items: [
          {
            title: "Time-Efficient Processing",
            description: "Enhance image quality in seconds instead of spending hours with manual photo enhancer techniques using our AI image enhancer",
            icon: "RiTimeLine",
            image: {
              src: "https://pic.kontext-dev.com/time-efficient-processing.webp",
              alt: "Time-Efficient Image Enhancement"
            }
          },
          {
            title: "Professional-Grade Results",
            description: "Get high-quality enhanced images suitable for printing, sharing, and professional use with our advanced photo enhancer technology",
            icon: "RiMedalLine",
            image: {
              src: "https://pic.kontext-dev.com/professional-grade-results.webp",
              alt: "Professional-Grade Image Enhancement Results"
            }
          },
          {
            title: "User-Friendly Interface",
            description: "Simple interface designed for users of all skill levels - improve picture quality free of technical complexity with our image enhancer",
            icon: "RiUserSmileLine",
            image: {
              src: "https://pic.kontext-dev.com/user-friendly-interface.webp",
              alt: "Image before and after using Image Enhancer by Kontext Dev, showing AI enhancement result"
            }
          },
          {
            title: "Data Privacy Protection",
            description: "Your precious images are processed securely and not stored after image enhancement is complete using our AI photo enhancer",
            icon: "RiShieldLine",
            image: {
              src: "https://pic.kontext-dev.com/data-privacy-protection.webp",
              alt: "Image enhancement with privacy protection by Kontext Dev"
            }
          }
        ]
      }} />}

      {/* Usage steps */}
      {page.usage && <Feature3 section={{
        ...page.usage,
        title: "How to Enhance Image Quality with Our AI Image Enhancer",
        description: "Enhance image quality and improve picture quality in just four simple steps with our AI image enhancer technology",
        items: [
          {
            title: "Upload Your Image",
            description: "Select and upload the image you want to enhance using our photo enhancer tool",
            icon: "RiUploadLine"
          },
          {
            title: "AI Quality Analysis",
            description: "Our AI image enhancer will automatically analyze your image and identify areas for quality improvement and enhancement",
            icon: "RiScanLine"
          },
          {
            title: "Preview Enhanced Result",
            description: "Check the preview to see how our image enhancer has improved picture quality while preserving original details",
            icon: "RiEyeLine"
          },
          {
            title: "Download Enhanced Image",
            description: "Download your high-quality enhanced image with improved resolution and sharpened details ready for any use",
            icon: "RiDownloadLine"
          }
        ]
      }} />}

      {/* Key Features section */}
      {page.feature && <Feature section={{
        ...page.feature,
        title: "Key Features of Our AI Image Enhancer",
        description: "Discover the powerful capabilities that make our image enhancer services stand out from other photo enhancer solutions",
        items: [
          {
            title: "Advanced AI Algorithm",
            description: "Our AI image enhancer utilizes state-of-the-art artificial intelligence to intelligently enhance image quality and improve picture quality while preserving natural appearance.",
            icon: "RiAiGenerate"
          },
          {
            title: "Multi-Type Enhancement Support",
            description: "Our photo enhancer services can handle various enhancement needs including sharpening images, improving resolution of image, color enhancement, and overall picture quality improvement.",
            icon: "RiImageEditLine"
          },
          {
            title: "High Resolution Processing",
            description: "Our image enhancer technology processes high-resolution images without quality loss, ensuring your enhanced images maintain superior clarity and detail.",
            icon: "RiImageLine"
          },
          {
            title: "Intelligent Image Enhancement",
            description: "With our AI photo enhancer, the technology intelligently analyzes and enhances different areas of your images, ensuring natural and professional enhancement results.",
            icon: "RiPaintBrushLine"
          },
          {
            title: "Fast Processing Speed",
            description: "Our image enhancer services quickly enhance image quality with optimized algorithms that deliver professional results in seconds, saving you valuable time.",
            icon: "RiTimerFlashLine"
          },
          {
            title: "Cloud-Based Solution",
            description: "Our online image enhancer operates in the cloud, so there's no need to download image enhancer software or worry about your device's processing power.",
            icon: "RiCloudLine"
          }
        ]
      }} />}

      {/* Stats section - REMOVED */}

      {/* Pricing Section */}
      <div className="container py-12 text-center">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl font-bold tracking-tight mb-4">Need More Advanced Image Enhancer Features?</h2>
          <p className="text-xl text-muted-foreground mb-8">
            Upgrade to our premium image enhancer services for batch processing, higher resolution support, and priority processing for your AI image enhancement projects
          </p>
          <Link href="/pricing">
            <Button size="lg" className="gap-2">
              View Premium Image Enhancer Plans
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M5 12h14" />
                <path d="m12 5 7 7-7 7" />
              </svg>
            </Button>
          </Link>
        </div>
      </div>

      {/* Testimonials */}
      {page.testimonial && <Testimonial section={{
        ...page.testimonial,
        title: "What Users Say About Our Image Enhancer Services",
        description: "Hear from photographers and professionals who use our AI image enhancer to enhance image quality and improve picture quality",
        items: [
          {
            description: "This image enhancer service transformed my photography workflow. I had a collection of photos that needed quality improvement, and the AI image enhancer enhanced them perfectly. The picture quality enhancement results exceeded my expectations.",
            title: "Anna P.",
            label: "Photography Enthusiast",
            image: {
              src: "https://pic.kontext-dev.com/7.webp",
              alt: "Anna P., Photography Enthusiast"
            }
          },
          {
            description: "I was skeptical about using AI image enhancer, but the results are impressive. My low-quality images were enhanced without any artifacts, and the picture quality remained natural. Best photo enhancer I've used.",
            title: "Clara H.",
            label: "Digital Artist",
            image: {
              src: "https://pic.kontext-dev.com/8.webp",
              alt: "Clara H., Digital Artist"
            }
          },
          {
            description: "As a content creator, I often need to enhance image quality for my projects. This AI photo enhancer makes it quick and easy to improve picture quality, with professional results every time.",
            title: "James L.",
            label: "Content Creator",
            image: {
              src: "https://pic.kontext-dev.com/9.webp",
              alt: "James L., Content Creator"
            }
          },
          {
            description: "I run a design studio and this AI image enhancer tool has become essential for our workflow. It helps us enhance image quality efficiently while maintaining professional standards for our clients.",
            title: "Mark V.",
            label: "Design Professional",
            image: {
              src: "https://pic.kontext-dev.com/10.webp",
              alt: "Mark V., Design Professional"
            }
          },
          {
            description: "The ability to enhance image quality without losing natural appearance has been a game changer for my marketing materials. This image enhancer free service is highly recommended for anyone needing quality improvement.",
            title: "Olivia W.",
            label: "Marketing Specialist",
            image: {
              src: "https://pic.kontext-dev.com/11.webp",
              alt: "Olivia W., Marketing Specialist"
            }
          },
          {
            description: "I've tried several photo enhancer software options, but this AI image enhancer stands out for its ease of use and quality results. It handles even low-resolution images with advanced AI image enhancement technology.",
            title: "Henry C.",
            label: "Image Processing Specialist",
            image: {
              src: "https://pic.kontext-dev.com/12.webp",
              alt: "Henry C., Image Processing Specialist"
            }
          }
        ]
      }} />}

      {/* FAQ section */}
      {page.faq && <FAQ section={{
        ...page.faq,
        title: "FAQ About Image Enhancer Services",
        description: "Common questions about our AI image enhancer technology and how to enhance image quality effectively",
        items: [
          {
            title: "Is this image enhancer service really free to use?",
            description: "Yes, our basic image enhancer features are completely free to use. You receive 10 free credits upon signing up to start enhancing image quality right away. For more advanced AI image enhancement needs, we also offer premium features in our paid version."
          },
          {
            title: "What types of image quality issues can your AI image enhancer handle?",
            description: "Our AI image enhancer can handle most common quality issues, including blurry images, low resolution photos, poor lighting, lack of sharpness, and overall picture quality improvement. The photo enhancer technology is effective for various image enhancement needs."
          },
          {
            title: "How is the picture quality after using your image enhancer services?",
            description: "We use advanced AI image enhancer technology to significantly improve picture quality while maintaining natural appearance. Our algorithms are designed to sharpen images, enhance details, and improve resolution of image naturally. Results may vary depending on original image quality."
          },
          {
            title: "Are there file size or image count limitations for the image enhancer free version?",
            description: "The image enhancer free version has certain limitations: maximum file size of 10MB and each image enhancement uses 5 credits. Currently, our AI photo enhancer supports processing one image at a time for quality enhancement."
          },
          {
            title: "How long does it take to enhance image quality using your AI image enhancer?",
            description: "Most images are enhanced within 10-30 seconds using our AI image enhancer, depending on the complexity of the enhancement needed and the image size. Our cloud-based photo enhancer ensures fast results without taxing your device."
          },
          {
            title: "Can I use the enhanced images commercially after using your image enhancer services?",
            description: "Yes, you can use your enhanced images commercially as long as you own the original images. Our image enhancer service simply improves picture quality of your existing images. Always ensure you have proper rights to the original images before commercial use."
          },
          {
            title: "What kind of images work best with the AI photo enhancer technology?",
            description: "For optimal image enhancement results, we recommend using clear digital images or high-quality scans. Our AI image enhancer works best with JPEG, PNG, and WebP formats, and can effectively improve picture quality for portraits, landscapes, product photos, and other image types."
          },
          {
            title: "Is my data safe when I use the image enhancer service?",
            description: "We take data privacy very seriously. The images you upload to our AI image enhancer are used only for enhancement processing and are not stored on our servers after the process is complete. We adhere to strict privacy policies to ensure the security of your images."
          }
        ]
      }} />}

      {/* Call to action */}
      {page.cta && <CTA section={{
        ...page.cta,
        title: "Try Our Image Enhancer Service Now",
        description: "Enhance image quality easily with our AI-powered image enhancer technology - get professional results in seconds and improve picture quality instantly",
        buttons: [
          {
            ...page.cta.buttons?.[0],
            title: "Start Enhancing Images",
            url: "#image-enhancer"
          }
        ]
      }} />}
    </>
  );
} 