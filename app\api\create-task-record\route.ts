import { NextResponse } from "next/server";
import { getSupabaseClient } from "@/models/db";
import { getUserUuid } from "@/services/user";
import { v4 as uuidv4 } from 'uuid';

export const runtime = 'edge';

export async function POST(request: Request) {
  try {
    const { taskId, prompt, status = 'processing' } = await request.json();

    if (!taskId || !prompt) {
      return NextResponse.json(
        { error: "Task ID and prompt are required" },
        { status: 400 }
      );
    }

    // Get user UUID from session
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { error: "User not authenticated" },
        { status: 401 }
      );
    }

    console.log(`Creating task record for user ${userUuid}, task ${taskId}`);

    // Get Supabase client
    const supabase = getSupabaseClient();

    // Create task record with all required fields
    const callbackId = uuidv4();
    const recordData = {
      user_uuid: userUuid,
      task_id: taskId,
      callback_id: callbackId,
      prompt: prompt,
      status: status,
      created_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('4o_generations')
      .insert(recordData)
      .select();

    if (error) {
      console.error('Error creating task record:', error);
      return NextResponse.json(
        { error: "Failed to create task record", details: error.message },
        { status: 500 }
      );
    }

    console.log('Task record created successfully:', data);

    return NextResponse.json({
      success: true,
      message: "Task record created successfully",
      taskId: taskId
    });

  } catch (error) {
    console.error("Error processing request:", error);
    return NextResponse.json(
      { error: "Failed to process request" },
      { status: 500 }
    );
  }
}
