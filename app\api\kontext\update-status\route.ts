import { NextResponse } from "next/server";
import { getUserUuid } from "@/services/user";
import { getSupabaseClient } from "@/models/db";

export const runtime = 'edge';

export async function POST(request: Request) {
  try {
    // Parse request body
    const requestBody = await request.json();
    const { taskId, status, generatedImageUrl, completed_at } = requestBody;
    
    console.log('Received update record status request:', { 
      taskId, 
      status, 
      generatedImageUrl: generatedImageUrl?.substring(0, 50) + '...',
      completed_at
    });
    
    // Validate required parameters
    if (!taskId) {
      return NextResponse.json(
        { error: "Task ID is required" },
        { status: 400 }
      );
    }
    
    // Get user UUID
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { error: "Unable to verify user identity" },
        { status: 401 }
      );
    }
    
    // Connect to database
    const supabase = getSupabaseClient();
    
    // Try different task ID formats
    const taskIdVariations = [
      taskId,
      taskId.startsWith('fluxkontext_') ? taskId : `fluxkontext_${taskId}`,
      taskId.includes('fluxkontext_') ? taskId.replace('fluxkontext_', '') : taskId,
      // 增加对kontextdev前缀的支持
      taskId.startsWith('kontextdev_') ? taskId : `kontextdev_${taskId}`,
      taskId.includes('kontextdev_') ? taskId.replace('kontextdev_', '') : taskId
    ];
    
    let updateSuccess = false;
    let updateResult = null;
    
    // Try updating records using different task ID formats
    for (const currentTaskId of taskIdVariations) {
      console.log(`Trying to update record with task ID format [${currentTaskId}]`);
      
      // Check if record exists
      const { data: existingRecord, error: checkError } = await supabase
        .from("4o_generations")
        .select("id, status")
        .eq("task_id", currentTaskId)
        .eq("user_uuid", userUuid)
        .maybeSingle();
        
      if (checkError) {
        console.error(`Error querying record with task ID [${currentTaskId}]:`, checkError);
        continue;
      }
      
      if (!existingRecord) {
        console.log(`No record found with task ID [${currentTaskId}]`);
        continue;
      }
      
      console.log(`Found record with task ID [${currentTaskId}], current status: ${existingRecord.status}`);
      
      // If record is already in completed status, skip update
      if (existingRecord.status === 'COMPLETED') {
        console.log(`Record with task ID [${currentTaskId}] is already in completed status, skipping update`);
        updateSuccess = true;
        updateResult = { id: existingRecord.id, status: existingRecord.status };
        break;
      }
      
      // Update record
      const updateData: any = {
        status: status || 'COMPLETED',
        completed_at: completed_at || new Date().toISOString()
      };
      
      // If generated image URL is provided, update it as well
      if (generatedImageUrl) {
        updateData.generated_image_url = generatedImageUrl;
      }
      
      const { data, error } = await supabase
        .from("4o_generations")
        .update(updateData)
        .eq("task_id", currentTaskId)
        .eq("user_uuid", userUuid)
        .select();
        
      if (error) {
        console.error(`Error updating record with task ID [${currentTaskId}]:`, error);
        console.error("Error details:", {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        });
        continue;
      }
      
      console.log(`Successfully updated record with task ID [${currentTaskId}]:`, data);
      updateSuccess = true;
      updateResult = data;
      break;
    }
    
    if (!updateSuccess) {
      return NextResponse.json(
        { error: "No matching record found or update failed", triedTaskIds: taskIdVariations },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      message: "Record status updated successfully",
      result: updateResult
    });
    
  } catch (error) {
    console.error("Error processing update record status request:", error);
    return NextResponse.json(
      { error: "Internal server error", message: (error as Error).message },
      { status: 500 }
    );
  }
} 